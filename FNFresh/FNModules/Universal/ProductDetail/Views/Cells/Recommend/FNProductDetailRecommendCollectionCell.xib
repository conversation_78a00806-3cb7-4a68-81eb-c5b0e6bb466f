<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_0" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNProductDetailRecommendCollectionCell">
            <rect key="frame" x="0.0" y="0.0" width="123" height="171"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="123" height="171"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="Ghg-Mz-0UA">
                        <rect key="frame" x="0.0" y="0.0" width="123" height="153"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="jBV-HS-hPE">
                            <size key="itemSize" width="128" height="128"/>
                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                            <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                        </collectionViewFlowLayout>
                    </collectionView>
                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Vlc-mX-qRf">
                        <rect key="frame" x="0.0" y="153" width="123" height="18"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="18" id="oGT-bH-xqk"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="Vlc-mX-qRf" secondAttribute="bottom" id="5qQ-r6-mh6"/>
                <constraint firstItem="Ghg-Mz-0UA" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="EoH-Zi-Uwd"/>
                <constraint firstItem="Vlc-mX-qRf" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="Ksz-Ci-8Fx"/>
                <constraint firstItem="Ghg-Mz-0UA" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="VNd-bc-6JE"/>
                <constraint firstAttribute="trailing" secondItem="Vlc-mX-qRf" secondAttribute="trailing" id="ZGL-1E-hiV"/>
                <constraint firstAttribute="trailing" secondItem="Ghg-Mz-0UA" secondAttribute="trailing" id="ctc-MA-UE0"/>
                <constraint firstItem="Vlc-mX-qRf" firstAttribute="top" secondItem="Ghg-Mz-0UA" secondAttribute="bottom" id="dk0-Vk-7m9"/>
            </constraints>
            <size key="customSize" width="123" height="171"/>
            <connections>
                <outlet property="collectionView" destination="Ghg-Mz-0UA" id="y5Y-Ji-2fP"/>
                <outlet property="pageControlBgView" destination="Vlc-mX-qRf" id="T54-x0-O2M"/>
                <outlet property="pageControlViewHeight" destination="oGT-bH-xqk" id="fFa-F0-oQl"/>
            </connections>
            <point key="canvasLocation" x="70" y="63.625592417061611"/>
        </collectionViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
