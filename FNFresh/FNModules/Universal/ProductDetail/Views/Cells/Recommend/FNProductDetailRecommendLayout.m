//
//  FNProductDetailRecommendLayout.m
//  FNFresh
//
//  Created by ye<PERSON> on 2025/6/17.
//  Copyright © 2025 FeiNiu. All rights reserved.
//

#import "FNProductDetailRecommendLayout.h"

@interface FNProductDetailRecommendLayout()

@property (nonatomic, strong) NSMutableArray<UICollectionViewLayoutAttributes *> *layoutAttributes;
@property (nonatomic, assign) CGSize contentSize;

@end

@implementation FNProductDetailRecommendLayout

- (void)prepareLayout {
    [super prepareLayout];
    
    self.layoutAttributes = [NSMutableArray array];
    NSInteger numberOfSections = [self.collectionView numberOfSections];
    CGFloat pageWidth = self.collectionView.bounds.size.width;
    CGFloat pageHeight = self.collectionView.bounds.size.height;
    
    // 自动计算间距，确保商品均匀分布
    CGFloat totalItemWidth = self.itemWidth * 3; // 每行3个商品
    CGFloat availableWidth = pageWidth - self.sectionInset.left - self.sectionInset.right;
    CGFloat calculatedSpacing = (availableWidth - totalItemWidth) / 2;
    self.minimumInteritemSpacing = MAX(calculatedSpacing, 8); // 最小间距8
    
    for (NSInteger section = 0; section < numberOfSections; section++) {
        NSInteger numberOfItems = [self.collectionView numberOfItemsInSection:section];
        
        for (NSInteger item = 0; item < numberOfItems; item++) {
            NSIndexPath *indexPath = [NSIndexPath indexPathForItem:item inSection:section];
            UICollectionViewLayoutAttributes *attributes = [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:indexPath];
            
            NSInteger row = item / 3;
            NSInteger col = item % 3;
            
            CGFloat x = section * pageWidth + self.sectionInset.left + col * (self.itemWidth + self.minimumInteritemSpacing);
            CGFloat y = self.sectionInset.top + row * (self.itemHeight + self.minimumLineSpacing);
            
            attributes.frame = CGRectMake(x, y, self.itemWidth, self.itemHeight);
            [self.layoutAttributes addObject:attributes];
        }
    }
    
    self.contentSize = CGSizeMake(numberOfSections * pageWidth, pageHeight);
}

- (CGSize)collectionViewContentSize {
    return self.contentSize;
}

- (NSArray<UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect {
    return self.layoutAttributes;
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath {
    return [self.layoutAttributes objectAtIndex:indexPath.item + indexPath.section * [self.collectionView numberOfItemsInSection:indexPath.section]];
}

- (BOOL)shouldInvalidateLayoutForBoundsChange:(CGRect)newBounds {
    return YES;
}

#pragma mark - Data Source Methods

- (NSInteger)numberOfSectionsForDataCount:(NSInteger)dataCount {
    return ceil(dataCount / 6.0);
}

- (NSInteger)numberOfItemsInSection:(NSInteger)section forDataCount:(NSInteger)dataCount {
    NSInteger startIndex = section * 6;
    NSInteger remainingItems = dataCount - startIndex;
    return MIN(remainingItems, 6);
}

#pragma mark - Index Conversion

- (NSInteger)realIndexFromDisplayIndexPath:(NSIndexPath *)indexPath {
    return indexPath.section * 6 + indexPath.item;
}

@end 
