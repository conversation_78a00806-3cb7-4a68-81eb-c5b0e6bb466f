//
//  FNFreshProductDetailRecommendMerchandiseCell.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2017/8/30.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//

#import "FNFreshProductDetailRecommendMerchandiseCell.h"
#import "FNFreshCornerTagModel.h"
#import "FNFreshItemsCornerModel.h"
#import "FNTag.h"
#import "FNBubbleView.h"
#import "UIFont+FontType.h"

@interface FNFreshProductDetailRecommendMerchandiseCell()
@property (nonatomic,strong) id <SDWebImageOperation> operation;
@property (weak, nonatomic) IBOutlet UILabel *estimateTagLabel;
@property (nonatomic, strong) FNBubbleView *badgeView;

@end

@implementation FNFreshProductDetailRecommendMerchandiseCell

- (void)awakeFromNib {
    [super awakeFromNib];
//    self.priceLabel.fontStyle = FNFreshPriceLabelConfigFontStyle16_11;
    
    [self.priceLabel setConfig:[FNFreshPriceLabelPartConfig configWithFont:[UIFont fnHarmonyFontOfSize:14*Ratio weight:UIFontWeightBold]] AtPartOfPrice:FNFreshPriceLabelPartInteger];
    
    [self.priceLabel setConfig:[FNFreshPriceLabelPartConfig configWithFont:[UIFont fnHarmonyFontOfSize:14*Ratio weight:UIFontWeightBold]] AtPartOfPrice:FNFreshPriceLabelPartDecimal];
        
    [self.priceLabel setConfig:[FNFreshPriceLabelPartConfig configWithFont:[UIFont systemFontOfSize:14*Ratio weight:UIFontWeightSemibold]] AtPartOfPrice:FNFreshPriceLabelPartSign];
    
    [self.priceLabel setConfig:[FNFreshPriceLabelPartConfig configWithFont:[UIFont systemFontOfSize:11*Ratio]] AtPartOfPrice:FNFreshPriceLabelPartUnit];
    
    [self.priceLabel setConfig:[FNFreshPriceLabelPartConfig configWithTextColor:[UIColor hexString:@"#CCCCCC"]] AtPartOfPrice:FNFreshPriceLabelPartUnit];
    
    [self.linePriceLabel setConfig:
     [FNFreshPriceLabelPartConfig configWith:[UIFont systemFontOfSize:12.f weight:UIFontWeightRegular]
                                   textColor:[UIColor fn_999999]]
                   AtPartOfPrice:FNFreshPriceLabelPartLinePrice];

    self.titleLabel.textColor = [UIColor fn_colorWithColorKey:kFNBlack];
    self.itemsCornetView.hidden = YES;
    self.promotionCornerView.hidden = YES;
    
    self.merchandiseImageView.placeholderColor = [UIColor fn_colorWithColorKey:kFNImgPlaceholderKey];
    self.productImage = self.merchandiseImageView;
    
    self.tagLabel.numberOfLines = 0;
    self.tagLabel.lineBreakMode = NSLineBreakByCharWrapping;
}

- (IBAction)addToShapcartBtnTouched:(id)sender {
    //购物车按钮点击处理
    if (self.didTouchAddToCartBtn) {
        self.didTouchAddToCartBtn(_merchandiseModel);
    }
}

- (void)setMerchandiseModel:(FNFreshProductListMerchandiseModel *)merchandiseModel {
    _merchandiseModel = merchandiseModel;
    
    [self.priceLabel setPrice:merchandiseModel.productRealPrice unit:merchandiseModel.saleUnit];
    [self.linePriceLabel setPrice:@"" unit:@"" linePrice:merchandiseModel.linePrice];
    
//    self.titleLabel.text = merchandiseModel.productName;
    NSMutableArray *tagsArr = [NSMutableArray arrayWithArray:merchandiseModel.tags];
    if (merchandiseModel.exchangeCardLabel.count > 0) {
        for (FNTag *item in merchandiseModel.exchangeCardLabel) {
            item.cornerRadius = 2;
            item.rightMargin = 4;
            [tagsArr addObject:item];
        }
     }
    [self.titleLabel refreshLabelWithText:merchandiseModel.productName tags:tagsArr];
        
    [self.merchandiseImageView fn_setImageWithURL:[NSURL URLWithString:merchandiseModel.productPictureURL] placeholder:[UIImage fnFresh_imageNamed:@"icon_placeholder_white"]];
        
    //131 冷藏标
    if (merchandiseModel.coldTagUrl != nil && ![merchandiseModel.coldTagUrl isEqualToString:@""]) {
        self.coldTagImageView.hidden = false;
        [self.coldTagImageView fn_setImageWithURL:[NSURL URLWithString:merchandiseModel.coldTagUrl] placeholder:nil];
    } else {
        self.coldTagImageView.hidden = true;
    }
    
    ///190:有营销标则不显示XX人还买过，没有营销标才显示XX人还买过
    self.tagLabel.text = @"";
    self.estimateTagLabel.text = @"";
    if (merchandiseModel.deliveryTimeLabele.count > 0) {
        [self.tagLabel updateText:@"" tags: [NSArray arrayWithObject:[merchandiseModel.deliveryTimeLabele firstObject]]];
    } else if (merchandiseModel.items.count > 0) {
        [self.tagLabel updateText:@"" tags: merchandiseModel.items];
    } else {
        BOOL hasRecommendTag = merchandiseModel.recommendTag.length > 0 || merchandiseModel.estimateTag.length > 0;
        if (hasRecommendTag) {
            NSString *recommendText = merchandiseModel.recommendTag;
            if (merchandiseModel.estimateTag.length > 0) {
                recommendText = [NSString stringWithFormat:@"\"%@\"",merchandiseModel.estimateTag];
            }
            self.estimateTagLabel.text = recommendText;
        } else {
            self.estimateTagLabel.text = @"";
        }
    }
    
    //加购数量角标
    [self configureBadgeView:merchandiseModel];
}

- (void)loadImageWithImageUrl:(NSString *)imageUrl imageView:(UIImageView __weak *)imageView {
    self.operation = [[SDWebImageManager sharedManager] loadImageWithURL:[NSURL URLWithString:imageUrl] options:SDWebImageRetryFailed progress:nil completed:^(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
        CGFloat scale = [UIScreen mainScreen].scale;
        imageView.image = [UIImage imageWithCGImage:image.CGImage scale:scale orientation:image.imageOrientation];
    }];
}

- (void)configureBadgeView:(FNFreshProductListMerchandiseModel *)model {
    if (model.purchasedNum) {
        [self updatePurchasedNumBadge:model.purchasedNum];
    } else {
        self.badgeView.hidden = YES;
    }
    NSNumber *cartNum = [FNFreshUtils.shareInstance.skuNumsDic safeObjectForKey:model.productID];
    if (cartNum && cartNum.integerValue != 0) {
        [self updatePurchasedNumBadge:[NSString stringWithFormat:@"%ld", cartNum.integerValue]];
    } else {
        self.badgeView.hidden = YES;
    }
}

- (void)updatePurchasedNumBadge:(NSString *)count {
    if(count.integerValue != 0) {
//        self.model.purchasedNum = count;//本地数据源也更新一下加购数量
        self.badgeView.hidden = NO;
        self.badgeView.textString = [NSString stringWithFormat:@"x%@",count];
    } else {
        self.badgeView.hidden = YES;
    }
}

//加购数量
- (FNBubbleView *)badgeView {
    if (!_badgeView) {
        _badgeView = [FNBubbleView new];
        [self.addToCartButton addSubview:_badgeView];
        _badgeView.userInteractionEnabled = YES;
        _badgeView.hidden = YES;
        [_badgeView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(self.addToCartButton.mas_centerY).offset(-5);
            make.right.mas_equalTo(self.addToCartButton.mas_right).offset(4);
        }];
       }
    return _badgeView;
}


- (void)dealloc {
    if (self.operation) {
        [self.operation cancel];
        self.operation = nil;
    }
}

@end
