//
//  FNFreshProductDetailRecommendMerchandiseCell.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2017/8/30.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FNFreshProductListMerchandiseModel.h"
#import "FNFreshGradientTagView.h"
#import "FNFreshCornerView.h"
#import "FNTagLabel.h"
#import "FNFreshPriceLabel.h"

/**
 大数据推荐 商品item cell
 */
@interface FNFreshProductDetailRecommendMerchandiseCell : UICollectionViewCell
@property (weak, nonatomic) IBOutlet UIImageView *merchandiseImageView; //商品主图
@property (weak, nonatomic) IBOutlet FNTagLabel *titleLabel;
@property (weak, nonatomic) IBOutlet FNFreshPriceLabel *priceLabel; //
@property (weak, nonatomic) IBOutlet FNFreshPriceLabel *linePriceLabel;
@property (weak, nonatomic) IBOutlet UIButton *addToCartButton;
@property (weak, nonatomic) IBOutlet FNFreshGradientTagView *promotionCornerView; //148弃用
@property (weak, nonatomic) IBOutlet UIImageView *promotionImageView;
@property (weak, nonatomic) IBOutlet FNFreshCornerView *itemsCornetView; //148弃用
@property (weak, nonatomic) IBOutlet UIImageView *coldTagImageView;
@property (weak, nonatomic) IBOutlet FNTagLabel *tagLabel;

@property (nonatomic, strong) UIImageView *productImage;

@property (nonatomic,weak) FNFreshProductListMerchandiseModel *merchandiseModel;

/**
 点击购物车按钮回调
 */
@property (nonatomic,copy) void (^didTouchAddToCartBtn)(FNFreshProductListMerchandiseModel *merchandiseModel);

- (void)updatePurchasedNumBadge:(NSString *)count;

@end
