//
//  FNProductDetailRecommendPageControl.h
//  FNFresh
//
//  Created by ye<PERSON> on 2025/6/17.
//  Copyright © 2025 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <Masonry/Masonry.h>

NS_ASSUME_NONNULL_BEGIN

@protocol FNProductDetailRecommendPageControlDelegate <NSObject>
- (void)pageControl:(UIView *)pageControl didSelectPage:(NSInteger)page;
@end

@interface FNProductDetailRecommendPageControl : UIView

@property (nonatomic, weak) id<FNProductDetailRecommendPageControlDelegate> delegate;
@property (nonatomic, assign) NSInteger numberOfPages;
@property (nonatomic, assign) NSInteger currentPage;

- (void)setCurrentPage:(NSInteger)currentPage animated:(BOOL)animated;
- (void)updateWithNumberOfPages:(NSInteger)numberOfPages;

@end

NS_ASSUME_NONNULL_END 
