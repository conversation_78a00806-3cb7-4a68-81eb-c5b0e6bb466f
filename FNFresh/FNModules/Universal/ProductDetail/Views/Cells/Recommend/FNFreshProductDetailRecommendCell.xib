<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22154" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22130"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="251" id="KGk-i7-Jjw" customClass="FNFreshProductDetailRecommendCell">
            <rect key="frame" x="0.0" y="0.0" width="319" height="251"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="319" height="251"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fey-XW-7Li">
                        <rect key="frame" x="12" y="0.0" width="295" height="251"/>
                        <subviews>
                            <view contentMode="scaleToFill" placeholderIntrinsicWidth="323" placeholderIntrinsicHeight="60" translatesAutoresizingMaskIntoConstraints="NO" id="kfZ-uM-hVk" userLabel="topView">
                                <rect key="frame" x="0.0" y="0.0" width="295" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="VqR-A4-Yo3"/>
                                </constraints>
                            </view>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="c9a-o6-NUo">
                                <rect key="frame" x="8" y="50" width="279" height="186"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="186" id="zHQ-xG-onl"/>
                                </constraints>
                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="3.5" minimumInteritemSpacing="0.0" id="Qq3-sq-q6H">
                                    <size key="itemSize" width="50" height="50"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                            </collectionView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        <constraints>
                            <constraint firstItem="c9a-o6-NUo" firstAttribute="top" secondItem="kfZ-uM-hVk" secondAttribute="bottom" id="Jmg-Wj-SUO"/>
                            <constraint firstAttribute="bottom" secondItem="c9a-o6-NUo" secondAttribute="bottom" priority="950" constant="10" id="K1S-Q7-042"/>
                            <constraint firstItem="kfZ-uM-hVk" firstAttribute="leading" secondItem="fey-XW-7Li" secondAttribute="leading" id="OlQ-WK-cDY"/>
                            <constraint firstItem="kfZ-uM-hVk" firstAttribute="centerX" secondItem="c9a-o6-NUo" secondAttribute="centerX" id="QOP-UA-JiB"/>
                            <constraint firstAttribute="trailing" secondItem="c9a-o6-NUo" secondAttribute="trailing" constant="8" id="hQl-Mm-9vt"/>
                            <constraint firstAttribute="trailing" secondItem="kfZ-uM-hVk" secondAttribute="trailing" id="wys-D8-fc3"/>
                            <constraint firstItem="c9a-o6-NUo" firstAttribute="leading" secondItem="fey-XW-7Li" secondAttribute="leading" constant="8" id="xos-t3-KiA"/>
                            <constraint firstItem="kfZ-uM-hVk" firstAttribute="top" secondItem="fey-XW-7Li" secondAttribute="top" id="zON-zH-d1S"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="7"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" red="0.94891661410000006" green="0.9490789771" blue="0.94890636210000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="fey-XW-7Li" secondAttribute="trailing" constant="12" id="9bL-lm-A8J"/>
                    <constraint firstItem="fey-XW-7Li" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="12" id="Hv3-8e-Zbv"/>
                    <constraint firstAttribute="bottom" secondItem="fey-XW-7Li" secondAttribute="bottom" id="WqL-V0-yvd"/>
                    <constraint firstItem="fey-XW-7Li" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="b9q-Lj-uag"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="collectionView" destination="c9a-o6-NUo" id="Ykz-cO-2L7"/>
                <outlet property="collectionViewHeightConstraint" destination="zHQ-xG-onl" id="Lga-N0-GOH"/>
                <outlet property="topView" destination="kfZ-uM-hVk" id="G70-Wz-5Lb"/>
                <outlet property="topViewHeight" destination="VqR-A4-Yo3" id="u4W-14-XcQ"/>
            </connections>
            <point key="canvasLocation" x="88.799999999999997" y="12.593703148425789"/>
        </tableViewCell>
    </objects>
</document>
