//
//  FNProductDetailRecommendLayout.h
//  FNFresh
//
//  Created by ye<PERSON> on 2025/6/17.
//  Copyright © 2025 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface FNProductDetailRecommendLayout : UICollectionViewLayout

@property (nonatomic, assign) CGFloat itemWidth;
@property (nonatomic, assign) CGFloat itemHeight;
@property (nonatomic, assign) CGFloat minimumLineSpacing;
@property (nonatomic, assign) CGFloat minimumInteritemSpacing;
@property (nonatomic, assign) UIEdgeInsets sectionInset;

// 数据源方法
- (NSInteger)numberOfSectionsForDataCount:(NSInteger)dataCount;
- (NSInteger)numberOfItemsInSection:(NSInteger)section forDataCount:(NSInteger)dataCount;

// 从显示索引获取实际数据索引
- (NSInteger)realIndexFromDisplayIndexPath:(NSIndexPath *)indexPath;

@end

NS_ASSUME_NONNULL_END 
