//
//  FNProductDetailRecommendCollectionCell.m
//  FNFresh
//
//  Created by wxl on 2023/9/21.
//  Copyright © 2023 FeiNiu. All rights reserved.
//

#import "FNProductDetailRecommendCollectionCell.h"
#import "FNProductDetailRecommendLayout.h"
#import "FNProductDetailRecommendPageControl.h"

@interface FNProductDetailRecommendCollectionCell()<UICollectionViewDelegate,UICollectionViewDataSource>
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet UIView *pageControlBgView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *pageControlViewHeight;

@property (assign,nonatomic) CGFloat itemWidth;
@property (assign,nonatomic) CGFloat itemHeight;
@property (copy,nonatomic) NSArray *dataArray;
@property (nonatomic, assign) NSInteger tabIndex;
@property (strong, nonatomic)FNProductDetailRecommendLayout *layout;

@property (strong, nonatomic)FNProductDetailRecommendPageControl *pageControlView;

@end

@implementation FNProductDetailRecommendCollectionCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [self initlializeCollectionView];
    self.itemWidth = 106;
    self.itemHeight = 206;
    
    [self.pageControlBgView addSubview:self.pageControlView];
    [self.pageControlView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.pageControlBgView.mas_centerX);
        make.bottom.equalTo(self.pageControlBgView).offset(-5);
        make.height.mas_equalTo(8);
    }];
}

- (FNProductDetailRecommendLayout *)layout {
    if (!_layout) {
        _layout = [[FNProductDetailRecommendLayout alloc] init];
        _layout.itemWidth = 106;
        _layout.itemHeight = 206;
        _layout.minimumLineSpacing = 8;
        _layout.minimumInteritemSpacing = 8;
    }
    return _layout;
}

- (FNProductDetailRecommendPageControl *)pageControlView {
    if (!_pageControlView) {
        _pageControlView = [[FNProductDetailRecommendPageControl alloc] init];
    }
    return _pageControlView;
}

#pragma mark - UICollectionView Setup

- (void)initlializeCollectionView {
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    self.collectionView.scrollEnabled = YES;
    self.collectionView.pagingEnabled = YES;
    self.collectionView.collectionViewLayout = self.layout;
    
    [self.collectionView registerNib:[UINib nibWithNibName:NSStringFromClass([FNFreshProductDetailRecommendMerchandiseCell class]) bundle:[FNFreshBundleHandler fnFreshBundle]] forCellWithReuseIdentifier:NSStringFromClass([FNFreshProductDetailRecommendMerchandiseCell class])];
}

- (void)setUpDataArray:(NSArray *)dataArray
        goodsItemWidth:(CGFloat)goodsItemWidth
        goodsItemHeight:(CGFloat)goodsItemHeight
              tabIndex:(NSInteger)tabIndex
               scrollToLast:(BOOL)isScrollToLast {
    self.layout.itemWidth = goodsItemWidth;
    self.layout.itemHeight = goodsItemHeight;
    
    self.dataArray = dataArray;
    self.tabIndex = tabIndex;
    
    if (dataArray.count > 6) {
        self.pageControlViewHeight.constant = 18;
        // 更新 pageControl
        [self.pageControlView updateWithNumberOfPages:[self.layout numberOfSectionsForDataCount:self.dataArray.count]];
    } else {
        self.pageControlViewHeight.constant = 0;
    }
       
    [self.collectionView reloadData];
    
    if (dataArray.count > 6) {
        NSIndexPath *innerIndexPath = [NSIndexPath indexPathForItem:0 inSection:0];
        
        if (tabIndex == 0 && isScrollToLast) {
            innerIndexPath = [NSIndexPath indexPathForItem:0 inSection:[self.layout numberOfSectionsForDataCount:self.dataArray.count]-1];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            // 检查collectionView是否存在且indexPath有效
            if (self.collectionView &&
                innerIndexPath.section >= 0 &&
                innerIndexPath.section < [self.collectionView numberOfSections]) {
                
                [self.collectionView scrollToItemAtIndexPath:innerIndexPath atScrollPosition:UICollectionViewScrollPositionNone animated:NO];
                [self.pageControlView setCurrentPage:innerIndexPath.section animated:NO];
            }
        });
        
    }
}

#pragma mark - UICollectionViewDataSource

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return [self.layout numberOfSectionsForDataCount: self.dataArray.count];
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return [self.layout numberOfItemsInSection:section forDataCount: self.dataArray.count];
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    NSInteger realIndex = [self.layout realIndexFromDisplayIndexPath:indexPath];
    FNFreshProductListMerchandiseModel *merchandiseModel = [self.dataArray safeObjectAtIndex:realIndex];
    
    FNFreshProductDetailRecommendMerchandiseCell *merchandiseCell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshProductDetailRecommendMerchandiseCell class]) forIndexPath:indexPath];
    
    __weak typeof(self) weakSelf = self;
    __weak typeof(merchandiseCell) weakmerchandiseCell = merchandiseCell;
    //点击加入购物车按钮回调处理
    [merchandiseCell setDidTouchAddToCartBtn:^(FNFreshProductListMerchandiseModel *merchandiseModel){
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(recommendMerchandiseCell:didTouchRecommendItemAddShopcart:indexPath:tabIndex:)]) {
            NSIndexPath *originalIndexPath = [NSIndexPath indexPathForItem:realIndex inSection:0];
            [weakSelf.delegate recommendMerchandiseCell:weakmerchandiseCell didTouchRecommendItemAddShopcart:merchandiseModel indexPath:originalIndexPath tabIndex:weakSelf.tabIndex];
        }
    }];
    merchandiseCell.merchandiseModel = merchandiseModel;
    return merchandiseCell;
}

#pragma mark - UICollectionViewDelegate

- (void)collectionView:(UICollectionView *)collectionView willDisplayCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath {
    NSInteger realIndex = [self.layout realIndexFromDisplayIndexPath:indexPath];
    FNFreshProductListMerchandiseModel *merchandiseModel = [self.dataArray safeObjectAtIndex:realIndex];
    if (self.delegate && [self.delegate respondsToSelector:@selector(recommendMerchandiseAgentAppearReccommendMerchandise:tabIndex:)]) {
        [self.delegate recommendMerchandiseAgentAppearReccommendMerchandise:merchandiseModel tabIndex:self.tabIndex];
    }
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    NSInteger realIndex = [self.layout realIndexFromDisplayIndexPath:indexPath];
    FNFreshProductListMerchandiseModel *merchandiseModel = [self.dataArray safeObjectAtIndex:realIndex];
    NSIndexPath *originalIndexPath = [NSIndexPath indexPathForItem:realIndex inSection:0];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(didSelectRecommendMerchandise:indexPath:tabIndex:)]) {
        [self.delegate didSelectRecommendMerchandise:merchandiseModel indexPath:originalIndexPath tabIndex:self.tabIndex];
    }
}


#pragma mark - UIScrollViewDelegate

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    [self updatePageControl];
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if (!decelerate) {
        [self updatePageControl];
    }
}

- (void)updatePageControl {
    CGFloat pageWidth = self.collectionView.bounds.size.width;
    NSInteger currentPage = round(self.collectionView.contentOffset.x / pageWidth);
    [self.pageControlView setCurrentPage:currentPage animated:YES];
    if (self.pageChangedBlock) {
        self.pageChangedBlock(currentPage);
    }
}

@end
