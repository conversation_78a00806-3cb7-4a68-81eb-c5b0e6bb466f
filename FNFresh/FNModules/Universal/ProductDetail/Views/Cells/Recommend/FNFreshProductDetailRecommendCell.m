//
//  FNFreshProductDetailRecommendCell.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2017/8/30.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//

#import "FNFreshProductDetailRecommendCell.h"
#import "FNFreshProductDetailRecommendMerchandiseCell.h"
#import "FNFreshMerchandiseDetailRecommendItemModel.h"
#import "UIFont+FNFont.h"
#import "FNProductDetailRecommendTabView.h"
#import "FNProductDetailRecommendCollectionCell.h"
@interface FNFreshProductDetailRecommendCell ()<UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout,FNProductDetailRecommendCollectionCellDelegate>
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet UILabel *recommendTitleLabel;
@property (copy,nonatomic) NSArray *dataArray;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *collectionViewHeightConstraint;
@property (weak, nonatomic) IBOutlet UIView *topView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *topViewHeight;

@property (assign,nonatomic) CGFloat itemWidth;
@property (assign,nonatomic) CGFloat itemHeight;
@property (strong,nonatomic) FNFreshMerchandiseDetailRecommendItemModel *recommendItem;
@property (nonatomic, strong) FNProductDetailRecommendTabView *tabView;
@property (nonatomic, assign) CGFloat collectionHeight;
@property (nonatomic, assign) BOOL isClickTab;
@property (nonatomic, assign) NSInteger tab0CurrentPage; // 第一个tab的当前页码
@property (nonatomic, assign) NSInteger tab1CurrentPage; // 第二个tab的当前页码
@end

@implementation FNFreshProductDetailRecommendCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [self initlializeCollectionView];
    [self initlializeSegmentBar];
    self.itemWidth = 106;
    self.itemHeight = 206;
}

- (void)initlializeCollectionView {
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    self.collectionView.scrollEnabled = YES;
    self.collectionView.pagingEnabled = YES;
    [self.collectionView registerNib:[UINib  nibWithNibName:NSStringFromClass([FNProductDetailRecommendCollectionCell class]) bundle:[FNFreshBundleHandler fnFreshBundle]] forCellWithReuseIdentifier:NSStringFromClass([FNProductDetailRecommendCollectionCell class])];
}

- (void)initlializeSegmentBar {
    self.tabView = [[FNProductDetailRecommendTabView alloc] initWithFrame:CGRectMake(10, 12, SCREEN_WIDTH - 24, self.topView.frame.size.height - 21)];
//    self.tabView = [FNProductDetailRecommendTabView alloc]
    [self.topView addSubview:self.tabView];
    WS(weakSelf);
    self.tabView.handleSelectTopItem = ^(NSInteger index) {
        weakSelf.recommendItem.selectIndex = index;
        weakSelf.isClickTab = YES;
        if (index == 0) {
            weakSelf.recommendItem.needScrollToLast = false;
        } else {
            weakSelf.recommendItem.needScrollToLast = true;
        }
        
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(productDetailNeedRelodCollectionCell:)]) {
            [weakSelf.delegate productDetailNeedRelodCollectionCell:weakSelf];
        }
        if (index <= weakSelf.dataArray.count) {
            NSIndexPath *indexPath = [NSIndexPath indexPathForRow:0 inSection:index];
            [weakSelf.collectionView scrollToItemAtIndexPath:indexPath atScrollPosition:UICollectionViewScrollPositionNone animated:NO];
        }
    };
    
    self.tabView.scrollSelectTopItem = ^(NSInteger index) {
        weakSelf.recommendItem.selectIndex = index;
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(productDetailNeedRelodCollectionCell:)]) {
            [weakSelf.delegate productDetailNeedRelodCollectionCell:weakSelf];
        }
    };

}

- (void)setMerchandiseItemModel:(FNFreshMerchandiseDetailItemModel *)merchandiseItemModel {
    [super setMerchandiseItemModel:merchandiseItemModel];
    self.recommendItem = (FNFreshMerchandiseDetailRecommendItemModel *)merchandiseItemModel;
    if (SCREEN_WIDTH < 24 + 16 + self.itemWidth*3) {
        self.itemWidth = (SCREEN_WIDTH - 24 - 16 - 16) / 3 - 2;
        self.itemHeight = 206 * self.itemWidth / 106;
    }
    
    if (self.recommendItem.selectIndex != 0) {
        [self.tabView tabItemSelectIndex:self.recommendItem.selectIndex];
    } else {
        if (self.recommendItem.isFirst) {
            self.recommendItem.isFirst = !self.recommendItem.isFirst;
            self.tabView.topBarTitleArray = self.recommendItem.tabList;
            // 首次进入时，确保needScrollToLast为false，从第一页开始显示
            self.recommendItem.needScrollToLast = false;
            // 初始化各tab的页码状态
            self.tab0CurrentPage = 0;
            self.tab1CurrentPage = 0;
            NSLog(@"11-11 第一次");
        }
    }
    
    self.dataArray = self.recommendItem.recommendList;
    self.collectionView.scrollEnabled = self.recommendItem.recommendList.count > 1;
    NSArray *currentTabArray = [self.recommendItem.recommendList safeObjectAtIndex:self.recommendItem.selectIndex];

    
    //  106 202 中间间距 8
    CGFloat height = currentTabArray.count > 3 ? self.itemHeight*2+8: self.itemHeight;
    if (currentTabArray.count > 6) {
        height += 18;
    }
    self.collectionHeight = ceilf(height);
    self.collectionViewHeightConstraint.constant = ceilf(height);
}

- (void)setDataArray:(NSArray *)dataArray {
    _dataArray = dataArray;
    [self.collectionView reloadData];
}

#pragma mark action

/**
 加入购物车处理
 */
- (void)didTouchAddToCartWithMerchandiseItem:(FNFreshProductListMerchandiseModel *)merchandiseModel recommendMerchandiseCell:(FNFreshProductDetailRecommendMerchandiseCell *)merchandiseCell indexPath:(NSIndexPath *)indexPath tabIndex:(NSInteger)tabIndex{
    //确定商品icon的center相对于recommendCell的point点
    CGPoint merchandiseCenter = merchandiseCell.merchandiseImageView.center;
    CGPoint center = [merchandiseCell convertPoint:merchandiseCenter toView:self.contentView];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(productDetailCell:didTouchRecommendItemAddShopcart:centerPoint:merchandiseImage:indexPath:tabIndex:)]) {
        [self.delegate productDetailCell:self didTouchRecommendItemAddShopcart:merchandiseModel centerPoint:center merchandiseImage:merchandiseCell.merchandiseImageView.image indexPath:indexPath tabIndex:tabIndex];
    }
}
//大数据推荐模块商品曝光
- (void)agentAppearReccommendMerchandise:(FNFreshProductListMerchandiseModel *)merchandiseModel tabIndex:(NSInteger)tabIndex {
    if (merchandiseModel.needTrackData) {
        if (self.delegate && [self.delegate respondsToSelector:@selector(productDetailAgentAppearReccommendMerchandise:merchandiseModel:tabIndex:)]) {
            [self.delegate productDetailAgentAppearReccommendMerchandise:self.recommendItem merchandiseModel:merchandiseModel tabIndex:tabIndex];
        }
        merchandiseModel.needTrackData = !merchandiseModel.needTrackData;
    }
}

#pragma mark UICollectionViewDelegate&UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return 1;
}

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return self.dataArray.count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    FNProductDetailRecommendCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNProductDetailRecommendCollectionCell class]) forIndexPath:indexPath];
    cell.delegate = self;
    NSLog(@"====>刷新的section:%ld",indexPath.section);
    [cell setUpDataArray:[self.recommendItem.recommendList safeObjectAtIndex:indexPath.section]
          goodsItemWidth:self.itemWidth
         goodsItemHeight:self.itemHeight
                tabIndex:indexPath.section
            scrollToLast:indexPath.section == 0 &&  self.recommendItem.needScrollToLast
             currentPage:indexPath.section == 0 ? self.tab0CurrentPage : self.tab1CurrentPage];
    // 记录页码变化
    __weak typeof(self) weakSelf = self;
    
    cell.pageChangedBlock = ^(NSInteger page, BOOL isScrollToLastPage) {
        if (indexPath.section == 0) {
            // 第一个tab：保存页码状态，用于数据刷新时恢复位置
            weakSelf.tab0CurrentPage = page;
            weakSelf.recommendItem.currentPage = page; // 保持兼容性
            // needScrollToLast只在滑动到最后一页时设置为true
            weakSelf.recommendItem.needScrollToLast = isScrollToLastPage;
            NSLog(@"====>第一个tab页码变化: page=%ld, needScrollToLast=%d", page, isScrollToLastPage);
        } else {
            // 第二个tab：保存页码状态，用于数据刷新时恢复位置
            weakSelf.tab1CurrentPage = page;
            // 任何页码变化都表示用户从第一个tab的最后一页过来了
            weakSelf.recommendItem.needScrollToLast = true;
            NSLog(@"====>第二个tab页码变化: page=%ld, needScrollToLast=true", page);
        }
    };
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    return CGSizeMake(SCREEN_WIDTH - 24 - 16, self.collectionHeight);
}


- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    self.isClickTab = NO;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (self.isClickTab) {
        return;
    }
    CGFloat offsetX = scrollView.contentOffset.x;
    CGFloat width = CGRectGetWidth(scrollView.bounds);
    NSInteger currentIndex = roundf(offsetX/width);
    if (currentIndex != self.recommendItem.selectIndex) {
        [self.tabView tabItemSelectIndex:currentIndex];
    }
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return 0;
}

//- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
//    return 10;
//}
//- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
//    if (section == 1) {
//        return UIEdgeInsetsMake(0, 12, 0, 0);
//    }
//    return UIEdgeInsetsZero;
//}

#pragma mark --FNProductDetailRecommendCollectionCellDelegate
///大数据推荐模块曝光
- (void)recommendMerchandiseAgentAppearReccommendMerchandise:(FNFreshProductListMerchandiseModel *)merchandiseModel tabIndex:(NSInteger)tabIndex{
    [self agentAppearReccommendMerchandise:merchandiseModel tabIndex:tabIndex];
}
/*
 点击推荐商品item
 */
- (void)didSelectRecommendMerchandise:(FNFreshProductListMerchandiseModel *)merchandiseItem indexPath:(NSIndexPath *)indexPath tabIndex:(NSInteger)tabIndex{
    if (self.delegate && [self.delegate respondsToSelector:@selector(productDetailCell:didSelectRecommendMerchandise:indexPath:tabIndex:)]) {
        [self.delegate productDetailCell:self didSelectRecommendMerchandise:merchandiseItem indexPath:indexPath tabIndex:tabIndex];
    }
}

/*
 点击推荐商品item加入购物车按钮
 */
- (void)recommendMerchandiseCell:(FNFreshProductDetailRecommendMerchandiseCell *)merchandiseCell didTouchRecommendItemAddShopcart:(FNFreshProductListMerchandiseModel *)merchandiseItem  indexPath:(NSIndexPath *)indexPath tabIndex:(NSInteger)tabIndex{
    [self didTouchAddToCartWithMerchandiseItem:merchandiseItem recommendMerchandiseCell:merchandiseCell indexPath:indexPath tabIndex:tabIndex];
}

@end
