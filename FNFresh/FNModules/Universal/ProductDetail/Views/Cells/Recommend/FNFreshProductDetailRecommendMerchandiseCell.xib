<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshProductDetailRecommendMerchandiseCell">
            <rect key="frame" x="0.0" y="0.0" width="145" height="235"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="145" height="235"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_placeholder" translatesAutoresizingMaskIntoConstraints="NO" id="eaj-zf-u6G">
                        <rect key="frame" x="0.0" y="0.0" width="145" height="145"/>
                        <constraints>
                            <constraint firstAttribute="width" secondItem="eaj-zf-u6G" secondAttribute="height" multiplier="1:1" id="4h3-dt-5ZD"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="海南大青柠檬100g海南大青柠檬海南大青柠檬" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="B2w-cf-euS" customClass="FNTagLabel">
                        <rect key="frame" x="5" y="150" width="135" height="33.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" red="0.40000000000000002" green="0.40000000000000002" blue="0.40000000000000002" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="18.8 /箱" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iXw-mN-YaZ" customClass="FNFreshPriceLabel">
                        <rect key="frame" x="5" y="200.5" width="101" height="15"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4iN-aI-Arl">
                        <rect key="frame" x="116" y="206" width="24" height="24"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="24" id="ULw-jl-mmn"/>
                            <constraint firstAttribute="width" constant="24" id="hcX-SO-nAU"/>
                        </constraints>
                        <state key="normal" image="icon_addshopingcat_small"/>
                        <connections>
                            <action selector="addToShapcartBtnTouched:" destination="gTV-IL-0wX" eventType="touchUpInside" id="2vg-KX-ZUc"/>
                        </connections>
                    </button>
                    <view contentMode="scaleToFill" fixedFrame="YES" placeholderIntrinsicWidth="60" placeholderIntrinsicHeight="24" translatesAutoresizingMaskIntoConstraints="NO" id="8bo-tS-jnM" customClass="FNFreshGradientTagView">
                        <rect key="frame" x="0.0" y="121" width="60" height="24"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </view>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" placeholderIntrinsicWidth="46" placeholderIntrinsicHeight="49" translatesAutoresizingMaskIntoConstraints="NO" id="7Hv-HH-tzl" userLabel="PromotionImageView">
                        <rect key="frame" x="5" y="3.5" width="46" height="49"/>
                    </imageView>
                    <view contentMode="scaleToFill" placeholderIntrinsicWidth="92" placeholderIntrinsicHeight="69" translatesAutoresizingMaskIntoConstraints="NO" id="hts-0Q-jKN" customClass="FNFreshCornerView">
                        <rect key="frame" x="5" y="0.0" width="92" height="69"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </view>
                    <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="43i-fs-6cr">
                        <rect key="frame" x="106" y="0.0" width="39" height="16"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="16" id="Bir-sM-4Os"/>
                            <constraint firstAttribute="width" constant="39" id="L3J-Dm-Kxi"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Rln-zW-6b4" customClass="FNTagLabel">
                        <rect key="frame" x="5" y="181" width="135" height="16"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="16" id="Vao-0h-Yjn"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="U36-BO-UVP">
                        <rect key="frame" x="5" y="182.5" width="135" height="14.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" red="1" green="0.42352941176470588" blue="0.035294117647058823" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uof-r4-qHZ" customClass="FNFreshPriceLabel">
                        <rect key="frame" x="5" y="215.5" width="31" height="14.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="Rln-zW-6b4" firstAttribute="leading" secondItem="B2w-cf-euS" secondAttribute="leading" id="0QB-Ky-hUE"/>
                <constraint firstItem="43i-fs-6cr" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="17J-ud-1Pg"/>
                <constraint firstAttribute="trailing" secondItem="B2w-cf-euS" secondAttribute="trailing" constant="5" id="1T8-qL-6X0"/>
                <constraint firstItem="iXw-mN-YaZ" firstAttribute="leading" secondItem="B2w-cf-euS" secondAttribute="leading" id="3pw-H1-8BR"/>
                <constraint firstItem="eaj-zf-u6G" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="87s-4W-CSf"/>
                <constraint firstItem="hts-0Q-jKN" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="8Hd-nW-xn1"/>
                <constraint firstItem="Rln-zW-6b4" firstAttribute="trailing" secondItem="B2w-cf-euS" secondAttribute="trailing" id="CDW-mX-ozx"/>
                <constraint firstItem="U36-BO-UVP" firstAttribute="trailing" secondItem="Rln-zW-6b4" secondAttribute="trailing" id="CGQ-Pe-lX2"/>
                <constraint firstAttribute="bottom" secondItem="4iN-aI-Arl" secondAttribute="bottom" constant="5" id="Kb3-sN-RZ7"/>
                <constraint firstAttribute="trailing" secondItem="43i-fs-6cr" secondAttribute="trailing" id="NSR-lv-cBd"/>
                <constraint firstAttribute="bottom" secondItem="uof-r4-qHZ" secondAttribute="bottom" constant="5" id="OHx-Dk-Md5"/>
                <constraint firstItem="4iN-aI-Arl" firstAttribute="leading" secondItem="iXw-mN-YaZ" secondAttribute="trailing" constant="10" id="OvJ-gB-aVj"/>
                <constraint firstItem="4iN-aI-Arl" firstAttribute="top" secondItem="Rln-zW-6b4" secondAttribute="bottom" constant="9" id="RML-zm-vi0"/>
                <constraint firstItem="B2w-cf-euS" firstAttribute="top" secondItem="eaj-zf-u6G" secondAttribute="bottom" constant="5" id="bZy-XR-eZD"/>
                <constraint firstItem="B2w-cf-euS" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="cTC-Jv-aJu"/>
                <constraint firstItem="7Hv-HH-tzl" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="fKo-ym-sS1"/>
                <constraint firstItem="eaj-zf-u6G" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="ghN-zp-n9M"/>
                <constraint firstItem="7Hv-HH-tzl" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="3.5" id="l1K-IY-r4W"/>
                <constraint firstItem="U36-BO-UVP" firstAttribute="bottom" secondItem="Rln-zW-6b4" secondAttribute="bottom" id="njG-c2-tkI"/>
                <constraint firstItem="uof-r4-qHZ" firstAttribute="top" secondItem="iXw-mN-YaZ" secondAttribute="bottom" id="qgj-5d-3Kd"/>
                <constraint firstItem="U36-BO-UVP" firstAttribute="leading" secondItem="Rln-zW-6b4" secondAttribute="leading" id="v6Q-ty-tyP"/>
                <constraint firstItem="uof-r4-qHZ" firstAttribute="leading" secondItem="iXw-mN-YaZ" secondAttribute="leading" id="vK7-9x-USd"/>
                <constraint firstItem="hts-0Q-jKN" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="w0G-ey-bIK"/>
                <constraint firstAttribute="trailing" secondItem="4iN-aI-Arl" secondAttribute="trailing" constant="5" id="wJL-5K-zia"/>
                <constraint firstAttribute="trailing" secondItem="eaj-zf-u6G" secondAttribute="trailing" id="zqL-j8-2vf"/>
            </constraints>
            <size key="customSize" width="145" height="235"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                    <real key="value" value="0.0"/>
                </userDefinedRuntimeAttribute>
                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                    <color key="value" red="0.94901960784313721" green="0.94901960784313721" blue="0.94901960784313721" alpha="1" colorSpace="calibratedRGB"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="addToCartButton" destination="4iN-aI-Arl" id="7gT-um-Jbf"/>
                <outlet property="coldTagImageView" destination="43i-fs-6cr" id="h72-XO-Kxg"/>
                <outlet property="estimateTagLabel" destination="U36-BO-UVP" id="4AB-nm-Mbq"/>
                <outlet property="itemsCornetView" destination="hts-0Q-jKN" id="w8G-nR-hrL"/>
                <outlet property="linePriceLabel" destination="uof-r4-qHZ" id="JE4-E7-wOF"/>
                <outlet property="merchandiseImageView" destination="eaj-zf-u6G" id="4YF-45-V8h"/>
                <outlet property="priceLabel" destination="iXw-mN-YaZ" id="Bgd-zY-biI"/>
                <outlet property="promotionCornerView" destination="8bo-tS-jnM" id="erg-2x-aNM"/>
                <outlet property="promotionImageView" destination="7Hv-HH-tzl" id="m7T-2R-r9f"/>
                <outlet property="tagLabel" destination="Rln-zW-6b4" id="lQI-RY-aa5"/>
                <outlet property="titleLabel" destination="B2w-cf-euS" id="s0O-Az-33i"/>
            </connections>
            <point key="canvasLocation" x="32.799999999999997" y="146.17691154422789"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="icon_addshopingcat_small" width="24" height="24"/>
        <image name="icon_placeholder" width="44" height="44"/>
    </resources>
</document>
