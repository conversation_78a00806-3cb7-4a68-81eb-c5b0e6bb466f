//
//  FNProductDetailRecommendCollectionCell.h
//  FNFresh
//
//  Created by wxl on 2023/9/21.
//  Copyright © 2023 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FNFreshProductListMerchandiseModel.h"
#import "FNFreshProductDetailRecommendMerchandiseCell.h"

NS_ASSUME_NONNULL_BEGIN
@protocol FNProductDetailRecommendCollectionCellDelegate <NSObject>
///大数据推荐模块曝光
- (void)recommendMerchandiseAgentAppearReccommendMerchandise:(FNFreshProductListMerchandiseModel *)merchandiseModel tabIndex:(NSInteger)tabIndex;
/*
 点击推荐商品item
 @param merchandiseItem 商品item模型参数
 */
- (void)didSelectRecommendMerchandise:(FNFreshProductListMerchandiseModel *)merchandiseItem indexPath:(NSIndexPath *)indexPath tabIndex:(NSInteger)tabIndex;

/*
 点击推荐商品item加入购物车按钮
 @param merchandiseItem 推荐商品item模型参数
 */
- (void)recommendMerchandiseCell:(FNFreshProductDetailRecommendMerchandiseCell *)merchandiseCell didTouchRecommendItemAddShopcart:(FNFreshProductListMerchandiseModel *)merchandiseItem indexPath:(NSIndexPath *)indexPath tabIndex:(NSInteger)tabIndex;
@end

@interface FNProductDetailRecommendCollectionCell : UICollectionViewCell

@property (nonatomic, weak) id <FNProductDetailRecommendCollectionCellDelegate> delegate;

// 新增：页码变化回调
@property (nonatomic, copy) void (^pageChangedBlock)(NSInteger page, BOOL isScrollToLastPage);

- (void)setUpDataArray:(NSArray *)dataArray
        goodsItemWidth:(CGFloat)goodsItemWidth
        goodsItemHeight:(CGFloat)goodsItemHeight
              tabIndex:(NSInteger)tabIndex
          scrollToLast:(BOOL)isScrollToLast;
@end

NS_ASSUME_NONNULL_END
