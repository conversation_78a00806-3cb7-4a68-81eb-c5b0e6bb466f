//
//  FNProductDetailRecommendPageControl.m
//  FNFresh
//
//  Created by ye<PERSON> on 2025/6/17.
//  Copyright © 2025 FeiNiu. All rights reserved.
//


#import "FNProductDetailRecommendPageControl.h"
#import "UIColor+Gradient.h"

@interface FNProductDetailRecommendPageControl()

@property (nonatomic, strong) NSMutableArray<UIView *> *pageIndicators;
@property (nonatomic, strong) UIView *selectedIndicator;

@end

@implementation FNProductDetailRecommendPageControl

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupPageControl];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self setupPageControl];
    }
    return self;
}

- (void)setupPageControl {
    self.pageIndicators = [NSMutableArray array];
    
    // 创建选中指示器（红色长方形）
    self.selectedIndicator = [[UIView alloc] init];
    self.selectedIndicator.backgroundColor = [UIColor fn_colorGradientChangeWithSize:CGSizeMake(12, 4) direction:FNGradientChangeDirectionUpwardDiagonalLine startColor:[UIColor hexString:@"#E60012"] endColor:[UIColor hexString:@"#FF3225"]];
    [self addSubview:self.selectedIndicator];
    
    [self.selectedIndicator mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(12);
        make.height.mas_equalTo(4);
        make.centerY.equalTo(self.mas_centerY);
    }];
    
    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTap:)];
    [self addGestureRecognizer:tapGesture];
}

- (void)updateWithNumberOfPages:(NSInteger)numberOfPages {
    _numberOfPages = numberOfPages;
    
    // 移除现有的指示器
    [self.pageIndicators makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self.pageIndicators removeAllObjects];
    
    // 创建新的指示器
    UIView *previousIndicator = nil;
    for (NSInteger i = 0; i < numberOfPages; i++) {
        UIView *indicator = [[UIView alloc] init];
        indicator.backgroundColor = [UIColor hexString:@"#CCCCCC"];
        [self addSubview:indicator];
        [self.pageIndicators addObject:indicator];
        
        [indicator mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.mas_centerY);
            make.width.height.mas_equalTo(4);
            if (i == 0) {
                make.leading.mas_equalTo(4);
            } else {
                make.leading.equalTo(previousIndicator.mas_trailing).offset(4);
            }
            if (i == numberOfPages - 1) {
                make.trailing.mas_equalTo(0);
            }
        }];
        
        previousIndicator = indicator;
    }
    
    // 重置选中指示器位置
    [self setCurrentPage:0 animated:NO];
    [self bringSubviewToFront:self.selectedIndicator];
}

- (void)setCurrentPage:(NSInteger)currentPage animated:(BOOL)animated {
    if (currentPage < 0 || currentPage >= self.pageIndicators.count) return;
    _currentPage = currentPage;

    // 重新布局所有灰色正方形
    UIView *previousIndicator = nil;
    for (NSInteger i = 0; i < self.pageIndicators.count; i++) {
        UIView *indicator = self.pageIndicators[i];
        [indicator mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.mas_centerY);
            make.width.height.mas_equalTo(4);
            
            // 计算每个指示器的间距
            CGFloat spacing = 4.0; // 默认间距
            if (i == 0) {
                // 第一个指示器
                make.leading.mas_equalTo(currentPage == 0 ? 4 : 0);
            } else {
                // 其他指示器
                if (i == currentPage || i == currentPage + 1) {
                    // 当前页和下一页之间的间距需要调整
                    spacing = 8.0;
                }
                make.leading.equalTo(previousIndicator.mas_trailing).offset(spacing);
            }
            if (i == self.pageIndicators.count - 1) {
                if (currentPage == self.pageIndicators.count - 1) {
                    make.trailing.mas_equalTo(-4);
                } else {
                    make.trailing.mas_equalTo(0);
                }
                
            }
        }];
        previousIndicator = indicator;
    }

    // 更新红色长方形位置
    UIView *targetIndicator = self.pageIndicators[currentPage];
    void (^updateSelectedIndicator)(void) = ^{
        [self.selectedIndicator mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(targetIndicator.mas_centerX);
            make.width.mas_equalTo(12);
            make.height.mas_equalTo(4);
            make.centerY.equalTo(self.mas_centerY);
        }];
    };

    if (animated) {
        [UIView animateWithDuration:0.3 animations:^{
            updateSelectedIndicator();
            [self layoutIfNeeded];
        }];
    } else {
        updateSelectedIndicator();
    }
}

- (void)handleTap:(UITapGestureRecognizer *)gesture {
    CGPoint location = [gesture locationInView:self];
    
    // 找到点击的指示器
    for (NSInteger i = 0; i < self.pageIndicators.count; i++) {
        UIView *indicator = self.pageIndicators[i];
        if (CGRectContainsPoint(indicator.frame, location)) {
            if (self.delegate && [self.delegate respondsToSelector:@selector(pageControl:didSelectPage:)]) {
                [self.delegate pageControl:self didSelectPage:i];
            }
            break;
        }
    }
}

@end 
